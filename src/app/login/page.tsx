"use client";

import React, { useState, useEffect } from "react";
import axios from "axios";
import { toast } from "react-hot-toast";
import { LoginForm } from "@/components/login-form";
import { Snowflake } from "lucide-react";

export default function LoginPage() {
  const [user, setUser] = useState({ email: "", password: "" });
  const [loading, setLoading] = useState(false);
  const [button, buttonDisabled] = useState(false);
  
  async function handleLogin() {
    try {
      setLoading(true);
      await axios
        .post("http://127.0.0.1:8000/login", user)
        .then((res) => {
          console.log(res.data);
          toast.success("Login successful");
          setLoading(false);
        })
        .catch((err) => {
          toast.error(err.response.data.error);
          setLoading(false);
        });
    } catch (error) {
      setLoading(false);
      console.log(error);
      toast.error("Something went wrong");
    }
  }

  // For monitoring the email password should not be empty so we use useEffect
  useEffect(() => {
    if (user.email.length > 0 && user.password.length > 0) {
      buttonDisabled(false);
    } else {
      buttonDisabled(true);
    }
  }, [user]);

  return (
    <div className="flex min-h-svh w-full items-center justify-center p-6 md:p-10 bg-gradient-to-br from-slate-50 to-slate-100">
      <div className="w-full max-w-sm">
        {/* ColdLeads Logo Section */}
        <div className="flex flex-col items-center mb-8">
          <div className="flex items-center space-x-3 mb-3">
            <div className="relative">
              <Snowflake className="h-10 w-10 text-emerald-600" />
              <div className="absolute -top-1 -right-1 h-4 w-4 bg-emerald-500 rounded-full flex items-center justify-center">
                <div className="h-2 w-2 bg-white rounded-full"></div>
              </div>
            </div>
            <div className="text-3xl font-bold bg-gradient-to-r from-emerald-600 to-emerald-800 bg-clip-text text-transparent">
              ColdLeads
            </div>
          </div>
          <p className="text-slate-600 text-center text-sm mb-2">
            Transform cold prospects into warm opportunities
          </p>
        </div>

        <LoginForm onClick={handleLogin}/>
        
        <div className="text-center text-xs text-slate-500 mt-6">
          © 2025 ColdLeads. All rights reserved.
        </div>
      </div>
    </div>
  );
}